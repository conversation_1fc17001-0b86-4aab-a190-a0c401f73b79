use reqwest;
use scraper::{Html, Selector};
use serde_json;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    let url = "https://www.allrecipes.com/recipe/112157/tuna-garden-casserole/";
    
    println!("Fetching URL: {}", url);
    
    // Fetch the HTML
    let client = reqwest::Client::builder()
        .timeout(std::time::Duration::from_secs(30))
        .build()?;
    let response = client
        .get(url)
        .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
        .send()
        .await?;
    
    let html = response.text().await?;
    let document = Html::parse_document(&html);
    
    println!("HTML fetched successfully, length: {}", html.len());
    
    // Try JSON-LD extraction first
    println!("\n=== JSON-LD EXTRACTION ===");
    let script_selector = Selector::parse("script[type='application/ld+json']").unwrap();
    let mut json_ld_found = false;
    
    for script in document.select(&script_selector) {
        let script_content = script.inner_html();
        if script_content.contains("Recipe") {
            json_ld_found = true;
            println!("Found JSON-LD script with Recipe data");
            println!("Script content length: {}", script_content.len());

            // Limit script content size to avoid parsing issues
            let limited_content = if script_content.len() > 100000 {
                println!("Script content too large ({}), truncating", script_content.len());
                &script_content[..100000]
            } else {
                &script_content
            };

            match serde_json::from_str::<serde_json::Value>(limited_content) {
                Ok(json_value) => {
                    println!("Successfully parsed JSON-LD");
                    // Handle both single recipe and array of recipes
                    let recipes = if json_value.is_array() {
                        json_value.as_array().unwrap().clone()
                    } else {
                        vec![json_value]
                    };

                    println!("Found {} recipe objects", recipes.len());

                    for (recipe_idx, recipe_data) in recipes.iter().enumerate() {
                        println!("Processing recipe object {}", recipe_idx + 1);

                        // Check if this is a Recipe type (handle both string and array formats)
                        let is_recipe = recipe_data.get("@type")
                            .map(|t| {
                                if let Some(type_str) = t.as_str() {
                                    type_str == "Recipe"
                                } else if let Some(type_array) = t.as_array() {
                                    type_array.iter().any(|item| item.as_str() == Some("Recipe"))
                                } else {
                                    false
                                }
                            })
                            .unwrap_or(false);

                        if is_recipe {
                            println!("Recipe name: {:?}", recipe_data.get("name"));

                            if let Some(ingredients_array) = recipe_data.get("recipeIngredient").and_then(|v| v.as_array()) {
                                println!("Raw ingredients from JSON-LD ({} total):", ingredients_array.len());
                                for (i, ingredient) in ingredients_array.iter().enumerate() {
                                    if let Some(ingredient_str) = ingredient.as_str() {
                                        println!("  {}: {}", i + 1, ingredient_str);
                                    }
                                }

                                // Apply the same filtering logic as the app
                                println!("\nAfter cleaning and validation:");
                                let mut valid_count = 0;
                                for (i, ingredient) in ingredients_array.iter().enumerate() {
                                    if let Some(ingredient_str) = ingredient.as_str() {
                                        let cleaned = clean_raw_ingredient_string(ingredient_str);
                                        let core_name = extract_core_ingredient_name(&cleaned);
                                        if !cleaned.is_empty() && is_valid_ingredient_name(&cleaned) {
                                            valid_count += 1;
                                            println!("  {}: {} -> {}", i + 1, ingredient_str, cleaned);
                                        } else {
                                            println!("  {}: {} -> FILTERED OUT (cleaned: '{}', core: '{}', valid: {})",
                                                   i + 1, ingredient_str, cleaned, core_name, is_valid_ingredient_name(&cleaned));
                                        }
                                    }
                                }
                                println!("Valid ingredients after filtering: {}", valid_count);
                            } else {
                                println!("No recipeIngredient array found");
                            }
                            break;
                        } else {
                            println!("Not a Recipe type: {:?}", recipe_data.get("@type"));
                        }
                    }
                }
                Err(e) => {
                    println!("Failed to parse JSON-LD: {}", e);
                    println!("Script content preview: {}", &limited_content[..std::cmp::min(500, limited_content.len())]);
                }
            }
            break;
        }
    }
    
    if !json_ld_found {
        println!("No JSON-LD with Recipe data found");
        
        // Try HTML extraction as fallback
        println!("\n=== HTML EXTRACTION FALLBACK ===");
        
        // Try AllRecipes sectioned ingredients
        if let Ok(section_selector) = Selector::parse(".mm-recipes-structured-ingredients__list-heading") {
            if let Ok(list_selector) = Selector::parse(".mm-recipes-structured-ingredients__list") {
                let sections = document.select(&section_selector).collect::<Vec<_>>();
                let lists = document.select(&list_selector).collect::<Vec<_>>();
                
                println!("Found {} sections and {} lists", sections.len(), lists.len());
                
                let mut total_ingredients = 0;
                for (i, section) in sections.iter().enumerate() {
                    let section_name = section.text().collect::<Vec<_>>().join(" ").trim().to_string();
                    println!("Section {}: {}", i + 1, section_name);
                    
                    if let Some(list) = lists.get(i) {
                        if let Ok(item_selector) = Selector::parse(".mm-recipes-structured-ingredients__list-item") {
                            let items: Vec<_> = list.select(&item_selector).collect();
                            println!("  Items in this section: {}", items.len());
                            
                            for (j, item) in items.iter().enumerate() {
                                let ingredient_text = item.text().collect::<Vec<_>>().join(" ").trim().to_string();
                                if !ingredient_text.is_empty() {
                                    total_ingredients += 1;
                                    println!("    {}: {}", j + 1, ingredient_text);
                                }
                            }
                        }
                    }
                }
                println!("Total ingredients from HTML sectioned extraction: {}", total_ingredients);
            }
        }
        
        // Try regular ingredient selectors
        println!("\n=== REGULAR HTML SELECTORS ===");
        let ingredient_selectors = [
            ".recipe-ingredient, .ingredients li, .ingredient",
            ".recipe-ingredients li, .ingredients-list li",
            ".recipe__ingredient, .recipe-card__ingredient",
            "[data-ingredient], .ingredient-text",
        ];
        
        for selector_str in &ingredient_selectors {
            if let Ok(selector) = Selector::parse(selector_str) {
                let found_ingredients: Vec<String> = document
                    .select(&selector)
                    .map(|el| el.text().collect::<Vec<_>>().join(" ").trim().to_string())
                    .filter(|s| !s.is_empty())
                    .collect();
                
                if !found_ingredients.is_empty() {
                    println!("Selector '{}' found {} ingredients:", selector_str, found_ingredients.len());
                    for (i, ingredient) in found_ingredients.iter().enumerate() {
                        println!("  {}: {}", i + 1, ingredient);
                    }
                    break;
                }
            }
        }
    }
    
    Ok(())
}

// Copy the cleaning and validation functions from the main app
fn clean_raw_ingredient_string(raw: &str) -> String {
    let mut cleaned = raw.trim().to_string();

    // Remove HTML entities and tags
    cleaned = cleaned.replace("&amp;", "&")
                    .replace("&lt;", "<")
                    .replace("&gt;", ">")
                    .replace("&quot;", "\"")
                    .replace("&#39;", "'");

    // Remove HTML tags if any
    if cleaned.contains('<') && cleaned.contains('>') {
        cleaned = regex::Regex::new(r"<[^>]*>").unwrap().replace_all(&cleaned, "").to_string();
    }

    // Fix common malformed patterns from AllRecipes
    if let Some(captures) = regex::Regex::new(r"^([a-zA-Z]+)\)\s+(.+)$").unwrap().captures(&cleaned) {
        let unit = &captures[1];
        let rest = &captures[2];
        let amount = match unit.to_lowercase().as_str() {
            "ounce" | "oz" => "8",
            "pound" | "lb" => "1",
            "cup" => "1",
            "tablespoon" | "tbsp" => "2",
            "teaspoon" | "tsp" => "1",
            _ => "1"
        };
        cleaned = format!("{} {} {}", amount, unit, rest);
    }

    cleaned = regex::Regex::new(r"^([a-zA-Z]+)\)\s+")
        .unwrap()
        .replace(&cleaned, "1 $1 ")
        .to_string();

    cleaned = regex::Regex::new(r"\s+").unwrap().replace_all(&cleaned, " ").trim().to_string();

    cleaned
}

fn is_valid_ingredient_name(name: &str) -> bool {
    let trimmed = name.trim();

    if trimmed.is_empty() {
        return false;
    }

    if !trimmed.chars().any(|c| c.is_alphabetic()) {
        return false;
    }

    // Extract the core ingredient name by removing common suffixes
    let core_ingredient = extract_core_ingredient_name(trimmed);

    // If the core ingredient is empty after cleaning, reject
    if core_ingredient.is_empty() {
        return false;
    }

    // Reject names that are ONLY preparation instructions (no actual ingredient)
    let invalid_only_names = [
        "chopped", "sliced", "diced", "minced", "beaten", "melted", "softened",
        "divided", "taste", "needed", "desired", "optional", "garnish",
        "spray", "leaf", "leaves", "caps", "whites", "yolks", "cubed",
        "halved", "quartered", "peeled", "seeded", "trimmed", "baby doll",
        "chopsticks for handles", "with flour", "for rolling", "juiced",
        "mashed", "crushed", "fresh", "dried", "frozen"
    ];

    let lower_core = core_ingredient.to_lowercase();
    if invalid_only_names.iter().any(|&invalid| lower_core == invalid) {
        return false;
    }

    // Check if it's ONLY a preparation method (like "ground" by itself)
    // But allow ingredients that contain preparation methods (like "ground black pepper")
    if regex::Regex::new(r"^(finely\s+)?(chopped|diced|sliced|minced|grated|shredded|crushed|ground|beaten|melted|softened|peeled|seeded|trimmed|halved|quartered)$")
        .unwrap()
        .is_match(&lower_core) {
        return false;
    }

    if regex::Regex::new(r"^[\d\s\/¼½¾⅓⅔⅛⅜⅝⅞\.]+\s*(ounce|pound|cup|tablespoon|teaspoon|gram|kilogram|liter|milliliter|inch)\s*$")
        .unwrap()
        .is_match(&lower_core) {
        return false;
    }

    true
}

fn extract_core_ingredient_name(name: &str) -> String {
    let mut cleaned = name.trim().to_string();

    // Remove common suffixes that indicate preparation or serving suggestions
    let suffixes_to_remove = [
        ", to taste",
        " to taste",
        ", as needed",
        " as needed",
        ", or to taste",
        " or to taste",
        ", or as needed",
        " or as needed",
        ", divided",
        " divided",
        ", optional",
        " optional"
    ];

    for suffix in &suffixes_to_remove {
        if cleaned.to_lowercase().ends_with(suffix) {
            let end_pos = cleaned.len() - suffix.len();
            cleaned = cleaned[..end_pos].trim().to_string();
        }
    }

    cleaned
}
